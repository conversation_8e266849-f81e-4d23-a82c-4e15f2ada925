<div
  class="create-profile-container"
  [ngClass]="{ 'arabic-settings': isArabic }"
  [dir]="dir"
>
  <button
    mat-icon-button
    class="close-button close-button-bs"
    (click)="closeDialog()"
  >
    <mat-icon class="close-icon" color="warn">close</mat-icon>
  </button>
  <form
    fxLayout="row"
    fxLayoutAlign="stretch"
    [formGroup]="formGroup"
    (ngSubmit)="formSubmit()"
  >
    <div class="basic-info-container" [dir]="dir">
      <div fxLayoutAlign="center">
        <app-avatar-upload
          (profilePic)="profilePicUploaded($event)"
          [profile]="data.profile"
        ></app-avatar-upload>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.lastName' | translate }}</mat-label>
          <input
            formControlName="lastName"
            [placeholder]="'profileDialog.lastName' | translate"
            [value]="data.profile.lastName"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.firstName' | translate }}</mat-label>
          <input
            formControlName="firstName"
            [placeholder]="'profileDialog.firstName' | translate"
            [value]="data.profile.firstName"
            matInput
          />
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.profileType' | translate }}</mat-label>
          <mat-select
            formControlName="title"
            [placeholder]="'profileDialog.profileType' | translate"
            [value]="data.profile.title"
            [disabled]="disableTitle"
          >
            <mat-option
              *ngFor="let profileType of PROFILE_TYPES"
              [value]="profileType"
            >
              {{ profileType | profileTypes | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.sex' | translate }}</mat-label>
          <mat-select
            formControlName="gender"
            [placeholder]="'profileDialog.sex' | translate"
            [value]="data.profile.gender"
            matInput
          >
            <mat-option value="" [disabled]="true"
              >{{ 'profileDialog.chooseSex' | translate }}
            </mat-option>
            <mat-option value="MALE">{{
              'general.sex.male' | translate
            }}</mat-option>
            <mat-option value="FEMALE">{{
              'general.sex.female' | translate
            }}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.phoneNumber' | translate }}</mat-label>
          <input
            formControlName="phoneNumber"
            placeholder="+212 ..."
            [value]="data.profile.phoneNumber"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.email' | translate }}</mat-label>
          <input
            formControlName="email"
            [placeholder]="'profileDialog.email' | translate"
            [value]="data.profile.email"
            type="email"
            matInput
          />
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.cardID' | translate }}</mat-label>
          <input
            formControlName="assignedID"
            [placeholder]="'profileDialog.cardID' | translate"
            [value]="data.profile.assignedID"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.birthDate' | translate }}</mat-label>
          <input
            matInput
            [matDatepicker]="picker"
            formControlName="birthDate"
          />
          <mat-datepicker-toggle
            matSuffix
            [for]="picker"
          ></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center" *ngIf="data.profile.title === 'PATIENT'">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.height' | translate }}</mat-label>
          <input
            formControlName="height"
            type="number"
            [placeholder]="'profileDialog.height' | translate"
            [value]="data.profile.height"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.weight' | translate }}</mat-label>
          <input
            formControlName="weight"
            type="number"
            [placeholder]="'profileDialog.weight' | translate"
            [value]="data.profile.weight"
            matInput
          />
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.insurance' | translate }}</mat-label>
          <input
            formControlName="insurance"
            [placeholder]="'profileDialog.insurance' | translate"
            [value]="data.profile.insurance"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.insuranceID' | translate }}</mat-label>
          <input
            formControlName="insuranceId"
            [placeholder]="'profileDialog.insuranceID' | translate"
            [value]="data.profile.insuranceId"
            matInput
          />
        </mat-form-field>
      </div>

      <mat-form-field class="w-100">
        <mat-label>{{ 'profileDialog.address' | translate }}</mat-label>
        <textarea
          formControlName="adress"
          [placeholder]="'profileDialog.address' | translate"
          matInput
        ></textarea>
      </mat-form-field>
      <div *ngIf="data.profile.title === 'PATIENT'">
        <mat-checkbox
          [checked]="false"
          color="primary"
          (change)="toggleExtra($event.checked)"
        >
          {{ 'profileDialog.showDetails' | translate }}
        </mat-checkbox>
      </div>
      <div
        *ngIf="
          data.profile &&
          data.profile._id &&
          (data.profile.title === 'DOCTOR' ||
            data.profile.title === 'RECEPTIONIST' || !data.profile.title)
        "
      >
        <mat-checkbox
          [checked]="false"
          color="primary"
          (change)="toggleExtra($event.checked)"
        >
          {{ 'profileDialog.showDetails' | translate }}
        </mat-checkbox>
      </div>
      <app-custom-button [loading]="isSending" [disabled]="!isValidForm()">{{
        (data.type === CALLS_TYPES.create
          ? 'general.addButton'
          : 'general.modifyButton'
        ) | translate
      }}</app-custom-button>
    </div>
    <mat-divider [vertical]="true" *ngIf="showExtra"></mat-divider>
    <div
      class="extra-info-container mt-3"
      fxLayout="column"
      *ngIf="showExtra && data.profile.title === 'PATIENT'"
    >
      <mat-form-field appearance="outline">
        <mat-label>{{ 'doctorSummary.allergies' | translate }}...</mat-label>
        <input
          matInput
          placeholder="Placeholder"
          #allergies
          (keyup.enter)="addItem($event, 'allergies')"
          (keydown.enter)="$event.preventDefault()"
          (focusout)="addItem($event, 'allergies')"
        />
        <img matSuffix src="assets/icons/antihistamines.svg" />
        <mat-chip-list
          *ngIf="$any(data)?.profile?.allergies?.length > 0; else noResults"
          aria-label="Allergies selection"
        >
          <mat-chip
            [removable]="true"
            (removed)="removeItem(allergy, 'allergies')"
            *ngFor="let allergy of data.profile.allergies"
            >{{ allergy }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
        </mat-chip-list>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label
          >{{ 'doctorSummary.chronicDiseases' | translate }}...</mat-label
        >
        <input
          matInput
          placeholder="Placeholder"
          #chronicDiseases
          (keyup.enter)="addItem($event, 'chronicDiseases')"
          (keydown.enter)="$event.preventDefault()"
          (focusout)="addItem($event, 'chronicDiseases')"
        />
        <img matSuffix src="assets/icons/bacteria.svg" />
        <mat-chip-list
          *ngIf="$any(data)?.profile?.chronicDiseases?.length > 0; else noResults"
          aria-label="Chronic Diseases selection"
        >
          <mat-chip
            (removed)="removeItem(chronicDisease, 'chronicDiseases')"
            [removable]="true"
            *ngFor="let chronicDisease of data.profile.chronicDiseases"
            >{{ chronicDisease }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
        </mat-chip-list>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label
          >{{ 'doctorSummary.permanentDrugs' | translate }}...</mat-label
        >
        <input
          matInput
          placeholder="Placeholder"
          #permanentDrug
          (keyup.enter)="addItem($event, 'permanentDrugs')"
          (keydown.enter)="$event.preventDefault()"
          (focusout)="addItem($event, 'permanentDrugs')"
        />
        <img matSuffix src="assets/icons/pill.svg" />
        <mat-chip-list
          *ngIf="$any(data)?.profile?.permanentDrugs?.length > 0; else noResults"
          aria-label="Permanent Drugs selection"
        >
          <mat-chip
            [removable]="true"
            (removed)="removeItem(permanentDrug, 'permanentDrugs')"
            *ngFor="let permanentDrug of data.profile.permanentDrugs"
            >{{ permanentDrug }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
        </mat-chip-list>
      </mat-form-field>
    </div>
    <div
      class="extra-info-container mt-3"
      fxLayout="column"
      fxLayoutAlign="space-between stretch"
      fxLayoutGap="7px"
      *ngIf="showExtra && data.profile.title === 'DOCTOR'"
    >
      <div appearance="outline" fxFlex="40" fxLayout="column" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="space-between center">
          <h5 class="profiles-label">Réceptionnistes</h5>

          <app-circle-button
            (click)="selectProfiles($event, 'RECEPTIONIST', 'receptionits')"
            name="add"
          >
          </app-circle-button>
        </div>
        <app-select-profiles
          (unselectedProfile)="unselectProfile($event)"
          [profiles]="receptionits || []"
          [profileType]="'RECEPTIONIST'"
        ></app-select-profiles>
      </div>

      <div appearance="outline" fxFlex="40" fxLayout="column" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="space-between center">
          <h5 class="profiles-label">Médecins subordonnés</h5>
          <app-circle-button
            (click)="selectProfiles($event, 'DOCTOR', 'doctors')"
            name="edit"
          ></app-circle-button>
        </div>
        <app-select-profiles
          (unselectedProfile)="unselectProfile($event)"
          [profiles]="doctors || []"
          [profileType]="'DOCTOR'"
        ></app-select-profiles>
      </div>
    </div>
    <div
      class="extra-info-container mt-3"
      fxLayout="column"
      *ngIf="showExtra && data.profile.title === 'RECEPTIONIST'"
    >
      <div appearance="outline" fxFlex="40" fxLayout="column" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="space-between center">
          <h5 class="profiles-label">Médecins subordonnés</h5>
          <app-circle-button
            (click)="selectProfiles($event, 'DOCTOR', 'doctors')"
            name="add"
          ></app-circle-button>
        </div>
        <app-select-profiles
          (unselectedProfile)="unselectProfile($event)"
          [profiles]="doctors || []"
          [profileType]="'DOCTOR'"
          [showProfile]="true"
        ></app-select-profiles>
      </div>
    </div>
  </form>
  <div class="seperator-content">
    <div class="empty-diagonal"></div>
  </div>
  <ng-template #noResults>
    <div fxLayoutAlign="center" class="mt-3">
      <h5>Aucune resultat</h5>
    </div>
  </ng-template>
</div>
