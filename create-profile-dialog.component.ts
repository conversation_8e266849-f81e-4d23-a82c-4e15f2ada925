import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import {
  MAT_BOTTOM_SHEET_DATA,
  MatBottomSheet,
  MatBottomSheetRef,
} from '@angular/material/bottom-sheet';
import { ProfileService } from '../../services/profile.service';
import { Profile } from '../../models/profile.model';
import { ErrorService } from '../../services/error.service';
import { CALLS_TYPES, PROFILE_TYPES } from '../../constants/defaults.consts';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { getArray } from '../../utils/array.functions';
import { SearchProfileComponent } from '../search-profile/search-profile.component';
import { MatDialog } from '@angular/material/dialog';
import { SearchProfileDialogComponent } from '../search-profile-dialog/search-profile-dialog.component';
import { StorageService } from 'src/app/core/services/storage.service';
import { TranslateService } from '@ngx-translate/core';
import { Direction } from '@angular/cdk/bidi';
import {Token} from '../../../core/models/token.model';

@Component({
  selector: 'app-create-profile-dialog',
  templateUrl: './create-profile-dialog.component.html',
  styleUrls: ['./create-profile-dialog.component.scss'],
})
export class CreateProfileDialogComponent implements OnInit {
  @ViewChild('allergies') inputAllergies: { nativeElement: { value: string } };
  @ViewChild('chronicDiseases') inputChronicDiseases: {
    nativeElement: { value: string };
  };
  @ViewChild('permanentDrug') inputPermanentDrug: {
    nativeElement: { value: string };
  };

  public formGroup: FormGroup;
  public isSending: boolean = false;
  public PROFILE_TYPES = Object.values(PROFILE_TYPES).filter(type => type !== PROFILE_TYPES.superAdmin);
  public CALLS_TYPES = CALLS_TYPES;
  public disableTitle: boolean = false;
  public profilePic: string = '';
  public showExtra: boolean;
  public receptionits: any[] = [];
  public doctors: any[] = [];
  public dir: Direction = 'ltr';
  public isArabic: boolean;
  public excludeTypes: string[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private profileService: ProfileService,
    private storageService: StorageService,
    private errorService: ErrorService,
    @Inject(MAT_BOTTOM_SHEET_DATA)
    public data: { type: string; profile: Profile, excludeTypes: string[] },
    private dialog: MatDialog,
    private bottomSheet: MatBottomSheet,
    public bottomSheetRef: MatBottomSheetRef<CreateProfileDialogComponent>,
    private translate: TranslateService
  ) {
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    this.isArabic = translate.currentLang === 'ar';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
      this.isArabic = translate.currentLang === 'ar';
    });
    bottomSheetRef.disableClose = true;
  }

  ngOnInit(): void {
    this.getPopulatedProfiles();
    this.setInitValues();
    this.generateForm();
  }

  setInitValues() {

    this.excludeTypes = this.data.excludeTypes || [];

    this.PROFILE_TYPES = this.PROFILE_TYPES.filter(type => !this.excludeTypes.includes(type));
    if (!this.data.profile.startingDate) {
      this.data.profile.startingDate = new Date();
    }
    if (this.data.profile.title) {
      this.disableTitle = true;
    }
    this.data.profile.allergies = getArray(this.data.profile?.allergies || []);
    this.data.profile.chronicDiseases = getArray(
      this.data.profile?.chronicDiseases || []
    );
    this.data.profile.permanentDrugs = getArray(
      this.data.profile?.permanentDrugs || []
    );
    if (!this.data.profile.birthDate) {
      this.data.profile.birthDate = new Date();
    }
    if (!this.data.profile.insurance) {
      this.data.profile.insurance = '';
    }
    if (!this.data.profile.insuranceId) {
      this.data.profile.insuranceId = '';
    }

    if (!this.data.profile.height) {
      this.data.profile.height = '';
    }
    if (!this.data.profile.weight) {
      this.data.profile.weight = '';
    }
    if (!this.data.profile.doctors) {
      this.data.profile.doctors = [];
    }
    if (!this.data.profile.receptionits) {
      this.data.profile.receptionits = [];
    }
    this.doctors = this.data.profile.doctors.map((x) => {
      return {
        staffId: x,
      };
    });
    this.receptionits = this.data.profile.receptionits.map((x) => {
      return {
        staffId: x,
      };
    });
  }

  generateForm() {
    this.formGroup = this.formBuilder.group({
      firstName: [this.data.profile?.firstName, [Validators.required]],
      lastName: [this.data.profile?.lastName, [Validators.required]],
      gender: [this.data.profile?.gender, [Validators.required]],
      phoneNumber: [this.data.profile?.phoneNumber, []],
      adress: [this.data.profile?.adress, []],
      birthDate: [this.data.profile?.birthDate, []],
      insurance: [this.data.profile?.insurance, []],
      insuranceId: [this.data.profile?.insuranceId, []],
      staffId: [this.data.profile?.staffId, []],
      height: [this.data.profile?.height, []],
      weight: [this.data.profile?.weight, []],
      email: new FormControl({
        value: this.data.profile.email,
        disabled:
          this.data.profile.email && this.data.profile.email?.length > 0,
      }),
      assignedID: new FormControl({
        value: this.data.profile?.assignedID,
        disabled: false,
      }),
      title: new FormControl(
        this.data.profile?.title,
        Validators.required
      ),
    });
  }

  isValidForm() {
    return this.formGroup.valid;
  }

  profilePicUploaded(profilePic: string) {
    this.profilePic = profilePic;
  }

  formSubmit() {
    this.isSending = true;
    if (this.data.profile._id) {
      this.formGroup.value._id = this.data.profile._id;
    }
    if (this.data.profile.email) {
      this.formGroup.value.email = this.data.profile.email;
    }
    let profile: Profile = {};
    profile = this.formGroup.value;
    profile.allergies = this.data.profile.allergies;
    profile.chronicDiseases = this.data.profile.chronicDiseases;
    profile.permanentDrugs = this.data.profile.permanentDrugs;
    profile.title = this.formGroup.value.title;
    profile.birthDate = this.formGroup.value.birthDate;
    profile.insurance = this.formGroup.value.insurance;
    profile.insuranceId = this.formGroup.value.insuranceId;
    profile.height = this.formGroup.value.height;
    profile.weight = this.formGroup.value.weight;
    profile.doctors = this.doctors.map((x) => x.staffId);
    profile.receptionits = this.receptionits.map((x) => x.staffId);
    if (this.profilePic && this.profilePic.length > 0) {
      profile.profilePic = this.profilePic;
    }

    const executeFunction =
      this.data.type === CALLS_TYPES.create ? 'createProfile' : 'updateProfile';
    this.profileService[executeFunction](profile).subscribe((res) => {
      this.storageService.refreshUserStorageSub().subscribe((strRes) => {
        this.isSending = false;

        this.storageService.clearUser();
        this.storageService.clearDoctors();
        const token: Token = {
          access_token: strRes.token,
        };
        this.storageService.storeDoctors(strRes.doctors);
        this.storageService.storeUser(strRes.user);
        // localStorage.removeItem(tokenStorageKey);
        this.storageService.storeToken(token);
        this.bottomSheet.dismiss(res);
      });
    }, this.errorService.handleError || (this.isSending = false));
  }

  addItem($event: any, type: string) {
    $event.preventDefault();
    if ($event?.target.value) {
      (this.data?.profile as any)[type].push($event?.target.value);
      switch (type) {
        case 'allergies':
          this.inputAllergies.nativeElement.value = '';
          break;
        case 'chronicDiseases':
          this.inputChronicDiseases.nativeElement.value = '';
          break;
        case 'permanentDrugs':
          this.inputPermanentDrug.nativeElement.value = '';
          break;
      }
    }
  }

  removeItem(name: string, type: string) {
    (this.data.profile as any)[type].splice(
      (this.data.profile as any)[type].indexOf(name),
      1
    );
  }

  closeDialog() {
    this.bottomSheetRef.dismiss();
  }

  toggleExtra(checked: boolean) {
    this.showExtra = checked;
  }
  selectProfiles($event?: Event, profileType?: string, attribute?: string) {
    if ($event) {
      $event.preventDefault();
    }
    const dialogComponent = this.dialog.open(SearchProfileDialogComponent, {
      width: '500px',
      data: {
        profileType,
        profiles: profileType === 'DOCTOR' ? this.doctors : this.receptionits,
      },
    });
    dialogComponent.afterClosed().subscribe((profiles) => {
      if (profiles) {
        profileType === 'DOCTOR'
          ? (this.doctors = this.doctors.concat(profiles))
          : (this.receptionits = this.receptionits.concat(profiles));
      }
    });
  }
  getPopulatedProfiles() {
    if (
      (this.data.profile &&
        this.data.profile.doctors &&
        this.data.profile.doctors.length > 0) ||
      (this.data.profile &&
        this.data.profile.receptionits &&
        this.data.profile.receptionits.length > 0)
    ) {
      this.profileService
        .findProfilesByIds(
          (this.data.profile.doctors || []).concat(
            this.data.profile.receptionits || []
          ),
          1,
          10
        )
        .subscribe((res) => {
          const profiles = res.docs;
          this.doctors = (this.data.profile.doctors || [])
            .map((x) => profiles.find((y: Profile) => x + '' === y.staffId + ''))
            .filter((x) => x);
          this.receptionits = (this.data.profile.receptionits || [])
            .map((x) => profiles.find((y: Profile) => x + '' === y.staffId + ''))
            .filter((x) => x);
        });
    }
  }
  unselectProfile(profile: Profile) {
    if (profile.title === 'DOCTOR') {
      this.doctors = this.doctors.filter((x) => x.staffId !== profile.staffId);
    } else {
      if (profile.title === 'RECEPTIONIST') {
        this.receptionits = this.receptionits.filter(
          (x) => x.staffId !== profile.staffId
        );
      }
    }
  }
}
