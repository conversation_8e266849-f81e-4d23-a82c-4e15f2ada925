import jwt from 'jsonwebtoken';
import Service from './Service';
import APIError from '../errors/APIError';
import config from '../../config/config';
import { sendEmail } from './../emails/sendEmail';
require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
import Profile from './../models/Profile';
import Staff from "./../models/Staff";
import {restructureProfileObj} from "../helpers/profileRework"

class UserService extends Service {
  constructor(model) {
    super(model);
    this.signIn = this.signIn.bind(this);
    this.signUp = this.signUp.bind(this);
    this.signOut = this.signOut.bind(this);
    this.resetPassword = this.resetPassword.bind(this);
    this.editUser = this.editUser.bind(this);
    this.isEmailTaken = this.isEmailTaken.bind(this);
    this.changePassword = this.changePassword.bind(this);
    this.refreshUserStorage=this.refreshUserStorage.bind(this);
    this.initializeUser=this.initializeUser.bind(this);
  }

  async signIn(email, password) {
    let user = await this.model.findOne({ email });
    if (!user) throw new APIError(401, 'Authentication failed, email not found');

    const isMatch = await user.comparePassword(password);
    if (!isMatch) throw new APIError(401, 'Authentication failed, wrong password');
    user.alreadyConnected = true;
    user.totalConnections++;
    user = await user.save().then(doc => doc.populate({
      path: 'profile',
      populate: [{path: "staff"}, {path: 'superAdmin'} , {path: 'hospital'} ]
    }).execPopulate());

    const userObj = Object.assign({},user._doc);
    const profileObj = Object.assign({},user.profile._doc);
    const hospitalObj = Object.assign({},user.profile.hospital._doc);

    hospitalObj.doctors = hospitalObj.doctors.map(d => d._id);
    hospitalObj.receptionists = hospitalObj.receptionists.map(r => r._id);
    hospitalObj.sessions = hospitalObj.sessions.map(s => s._id);
    if(hospitalObj.tenant) hospitalObj.tenant = {_id : hospitalObj.tenant._id, actif : hospitalObj.tenant.actif};

    profileObj.hospital = hospitalObj;
    userObj.profile = profileObj;

    const token = jwt.sign(userObj, process.env.JWT_SECRET_KEY || config.JWT_SECRET_KEY, {
      expiresIn: user.expiresIn || '24h' // Use user's expiresIn or default to 24 hours
    });
    let staffDoctors = user.profile.staff ? user.profile.staff.doctors: [];
    let doctors=await Staff.find({_id:{$in:staffDoctors}})
                            .populate({
                              path: "profile",
                              select: "firstName lastName title profilePic"
                            })
                            .select("-specialty");

    user.profile._doc = restructureProfileObj(user.profile._doc,true);
    doctors = doctors.map(d => d.profile);
    return {
      token,
      user,
      doctors
    };
  }

  async signUp(user) {
    const { password } = user;
    user = {
      email: user.email,
      password: user.password,
      expiresIn: user.expiresIn
    };
    user = new this.model(user);
    user = await user.save();
    if (!user) throw new APIError(401, 'wrong user format');

    return await this.signIn(user.email, password);
  }

  async signOut(user) {
    await this.model.findByIdAndUpdate(user._id, { $set: { alreadyConnected: false } });
    return 'CYA';
  }

  async isEmailTaken(email) {
    const user = await this.model.findOne({ email });
    if (user) return true;

    return false;
  }

  async editUser(user, token = null, password) {
    let fromEmailAuth = false;
    user = {
      email: user.email,
      password: user.password,
      expiresIn: user.expiresIn
    };
    //allow from the email auth
    if (token) {
      let userToken = jwt.verify(token, process.env.JWT_SECRET_KEY || JWT_SECRET_KEY, {
        algorithms: ['HS256']
      });
      if (userToken.tokenFromEmail) fromEmailAuth = true;
      user._id = userToken._id;
    };
    //allow from inside the account
    let oldUserInfo = await this.model.findById(user._id);
    if (!oldUserInfo) throw new APIError(404, 'user not found');
    const isMatch = await oldUserInfo.comparePassword(password);
    if (!isMatch && !fromEmailAuth) throw new APIError(401, 'Authentication failed, wrong password');
    oldUserInfo = Object.assign(oldUserInfo, user);
    if (!oldUserInfo) throw new APIError(401, 'cant edit the user infos');
    await oldUserInfo.save();
    token = jwt.sign(oldUserInfo.toJSON(), process.env.JWT_SECRET_KEY || config.JWT_SECRET_KEY);

    return {
      user: oldUserInfo,
      token
    };
  }

  async resetPassword(email, hostname) {
    let user = await this.model.findOne({ email }).populate({
      path: 'profile',
      populate: [{ path: 'hospital' }]
    });
    if (!user) throw new APIError(401, 'Email not found');

    const userObj = {
      _id: user._id,
      tokenFromEmail: true,
    };

    const token = jwt.sign(userObj, process.env.JWT_SECRET_KEY || config.JWT_SECRET_KEY,{
      expiresIn: '2h'
    });


    let url=hostname+'/auth/resetPassword/'+token;
     await sendEmail(email,"Réinitialisation de mot de passe", "Bonjour "+ user.profile.firstName.substring(0, 1).toUpperCase() + user.profile.firstName.substring(1)
     +',<br>Vous avez envoyé une demande de réinitialisation de mot de passe, <br> Pour compléter l\'opération appuyez sur le lien suivant :',url,undefined,"https://www.pinclipart.com/picdir/big/225-2251613_security-lock-icon-clipart.png");
    return "ok";
  }

  async changePassword(user, currentPassword, newPassword) {
    let userID=user._id;
    let fromEmailAuth=false;
    if (user.tokenFromEmail) fromEmailAuth = true;
     user = await this.model.findById(userID);
    if (!user) throw new APIError(401, 'user not found');
    if (((await user.comparePassword(currentPassword)) && newPassword && newPassword !== "") || (fromEmailAuth)) {
      user.password = newPassword;
      user._doc.password = newPassword;
      user = await this.model(user).save().then(doc => doc.populate({
        path: 'profile',
        populate: [{path: "staff"}, {path: 'superAdmin'} , {path: 'hospital'} ]
      }).execPopulate());

      const userObj = Object.assign({},user._doc);
      const profileObj = Object.assign({},user.profile._doc);
      const hospitalObj = Object.assign({},user.profile.hospital._doc);
      
      hospitalObj.doctors = hospitalObj.doctors.map(d => d._id);
      hospitalObj.receptionists = hospitalObj.receptionists.map(r => r._id);
      hospitalObj.sessions = hospitalObj.sessions.map(s => s._id);
      if(hospitalObj.tenant) hospitalObj.tenant = {_id : hospitalObj.tenant._id, actif : hospitalObj.tenant.actif};
      
      profileObj.hospital = hospitalObj;
      userObj.profile = profileObj;

      const token = jwt.sign(userObj, process.env.JWT_SECRET_KEY || config.JWT_SECRET_KEY);
      return { passwordChanged: true, message: 'password changed successfully',user,token };
    } else {
      return { passwordChanged: false, message: 'wrong password' };
    }
  }
  async refreshUserStorage(token){
    if (!token) throw new APIError(401, 'token not found');
    let user=jwt.verify(token, process.env.JWT_SECRET_KEY || JWT_SECRET_KEY, {
      algorithms: ['HS256']
    });
    if(!user.profile )
    throw new APIError(401, 'user not found');
    user=await this.model.findById(user._id).populate({
      path: 'profile',
      populate: [{path: "staff"}, {path: 'superAdmin'} , {path: 'hospital'} ]
    });

    const userObj = Object.assign({},user._doc);
    const profileObj = Object.assign({},user.profile._doc);
    const hospitalObj = Object.assign({},user.profile.hospital._doc);

    hospitalObj.doctors = hospitalObj.doctors.map(d => d._id);
    hospitalObj.receptionists = hospitalObj.receptionists.map(r => r._id);
    hospitalObj.sessions = hospitalObj.sessions.map(s => s._id);
    if(hospitalObj.tenant) hospitalObj.tenant = {_id : hospitalObj.tenant._id, actif : hospitalObj.tenant.actif};

    profileObj.hospital = hospitalObj;
    userObj.profile = profileObj;

    token = jwt.sign(userObj, process.env.JWT_SECRET_KEY || config.JWT_SECRET_KEY, {
      expiresIn: user.expiresIn || '24h'
    });
    let staffDoctors = user.profile.staff ? user.profile.staff.doctors: [];
    let doctors=await Staff.find({_id:{$in:staffDoctors}})
                            .populate({
                              path: "profile",
                              select: "firstName lastName title profilePic"
                            })
                            .select("-specialty");
    user.profile._doc = restructureProfileObj(user.profile , true);
    doctors = doctors.map(d => d.profile);
    return {
      token,
      user,
      doctors
    };
  }
  async initializeUser(profile , levelOfAccess){
    let password = "1234"; //default password for all staff

    // Generate email if not provided - use assignedID, phoneNumber, or auto-generate
    let email = profile.email;
    let isAutoGeneratedEmail = false;

    if (!email) {
      isAutoGeneratedEmail = true;
      if (profile.assignedID) {
        email = `${profile.assignedID}@${profile.hospital._id || 'hospital'}.local`;
      } else if (profile.phoneNumber) {
        email = `${profile.phoneNumber}@${profile.hospital._id || 'hospital'}.local`;
      } else {
        // Generate unique email using profile ID and timestamp
        email = `staff_${profile._id}_${Date.now()}@${profile.hospital._id || 'hospital'}.local`;
      }
    }

    let user = new this.model({
      email: email,
      password: password,
      levelOfAccess: levelOfAccess,
      profile: profile._id,
      isAutoGeneratedEmail: isAutoGeneratedEmail
    });

    user = await user.save().then(doc => doc.populate({
      path: 'profile',
      populate: [{ path: 'hospital' }/*,{ path: 'doctors',select:"firstName lastName title profilePic -specialty" }*/]
    }).execPopulate());;
    if (!user) throw new APIError(400, 'cannot create user');

    const userObj = {
      _id: user._id,
      tokenFromEmail: true,
    };

    const token = jwt.sign(userObj, process.env.JWT_SECRET_KEY || config.JWT_SECRET_KEY,{
      expiresIn: '2h'
    });

    let url=process.env.FRONTEND_URL+'/auth/resetPassword/'+token;
     await sendEmail(profile.email,"Réinitialisation de mot de passe", "Bonjour "+ user.profile.firstName.substring(0, 1).toUpperCase() + user.profile.firstName.substring(1)
     +',<br>Vous avez envoyé une demande de réinitialisation de mot de passe, <br> Pour compléter l\'opération appuyez sur le lien suivant :',url,undefined,"https://www.pinclipart.com/picdir/big/225-2251613_security-lock-icon-clipart.png");
  }
}

export default UserService;