import Service from './Service';
import APIError from '../errors/APIError';
import mongoose from 'mongoose';
import User from "../models/User";
import UserService from "../services/UserService";
import Appointment from "../models/Appointment";
import socket from "../../config/socket";
import jwt from 'jsonwebtoken';
import Staff from "../models/Staff";
import Patient from "../models/Patient";
import Supplier from "../models/Supplier";
import {STAFF_TITLES} from "../../config/utils/variables";
import {restructureProfileObj} from "../helpers/profileRework";
const userService = new UserService(User);

class ProfileService extends Service {
    constructor(model) {
        super(model);
        this.addProfile = this.addProfile.bind(this);
        this.editProfile = this.editProfile.bind(this);
        this.findOneProfile = this.findOneProfile.bind(this);
        this.findProfiles = this.findProfiles.bind(this);
        this.deleteProfiles = this.deleteProfiles.bind(this);
        this.restoreProfiles = this.restoreProfiles.bind(this);
        this.getProfilePic = this.getProfilePic.bind(this);
        this.getPatients=this.getPatients.bind(this);
        this.patientEditPatient=this.patientEditPatient.bind(this);
        this.patientGetPatient=this.patientGetPatient.bind(this);
        this.editLang=this.editLang.bind(this);

    }
    async addProfile(profile, levelOfAccess = 2, user, hostname = null) {
        //profile settings
        if(profile.email){
            const user = await User.findOne({email: profile.email});
            if(user) throw new APIError(404, 'Email already in use');
        }
        const profileObj = Object.assign({},profile)
        profile.createdBy = user.profile._id;
        profile.updatedBy = user.profile._id;
        profile.hospital = user.profile.hospital._id;
        profile = new this.model(profile);
        
        const isStaff = STAFF_TITLES.includes(profile.title);
        const isPatient = profile.title == "PATIENT";
        const isSupplier = profile.title == "SUPPLIER";

        if(isStaff){
            let staff = new Staff(profileObj);

            if(profile.title=="DOCTOR"){
                staff.doctors=[staff._id];
                let query={profile:mongoose.Types.ObjectId(user.profile._id),
                    hospital:mongoose.Types.ObjectId(user.profile.hospital._id)
                };
                let connectedStaff = await Staff.findOne(query);
                if(connectedStaff){
                    connectedStaff.doctors.push(staff._id);
                    await connectedStaff.save();
                }
            }
            if(profile.title=="RECEPTIONIST"){
                staff.receptionits=[staff._id];
                staff.doctors=[user.profile.staff._id]
            }

            profile.staff = staff._id;
            staff.profile = profile._id;
            await staff.save();

        }else if(isPatient){
            let patient = new Patient(profileObj);
            profile.patient = patient._id;
            patient.profile = profile._id;
            await patient.save();
        }else if(isSupplier){
            let supplier = new Supplier(profileObj);
            profile.supplier = supplier._id;
            supplier.profile = profile._id;
            await supplier.save();
        }

        profile = await profile.save();
        profile = await profile.populate([{path: "staff"} , {path: 'patient'} ,{path: "supplier"}]).execPopulate();;
        if (!profile) throw new APIError(400, 'cannot create profile');
        //user settings
        if (profile.email && isStaff) {
            await userService.initializeUser(profile,2);
        }
        profile = restructureProfileObj(profile,true);
        this.editProfile(profile,user);
        return profile;
    }

    async deleteProfiles(profileIDs, user) {
        profileIDs = profileIDs.map(x => mongoose.Types.ObjectId(x));
        let profiles = await this.model.softDeleteMany({ hospital: mongoose.Types.ObjectId(user.profile.hospital._id), _id: { $in: profileIDs } });
        if (!profiles) throw new APIError(400, 'cannot delete profiles');
        return profiles;
    }
    async restoreProfiles(profileIDs, user) {
        profileIDs = profileIDs.map(x => mongoose.Types.ObjectId(x));
        let profiles = await this.model.restore({ hospital: mongoose.Types.ObjectId(user.profile.hospital._id), _id: { $in: profileIDs } });
        if (!profiles) throw new APIError(400, 'cannot restore profiles');
        return profiles;
    }

    async editProfile(profile, user,socketID) {
        profile.updatedBy = user.profile._id;
        const isNew = !profile._id;
        const profileObj = Object.assign({} , profile);
        delete profileObj._id
        delete profileObj.createdAt
        delete profileObj.createdBy
        profile = new this.model(profile);
        profile = await this.model.findOneAndUpdate({_id:mongoose.Types.ObjectId(profile._id)}, profile, { new: true });

        const isStaff = STAFF_TITLES.includes(profile.title);
        const isPatient = profile.title == "PATIENT";
        const isSupplier = profile.title == "SUPPLIER";

        if(isStaff){
            let staff = await Staff.findOneAndUpdate({profile:mongoose.Types.ObjectId(profile._id)}, profileObj, { new: true });

            if(isNew) {
                staff.profile = profile._id;
                profile.staff = staff._id;
                await staff.save();
                await profile.save();
            }

            if(profile.title =="DOCTOR"){
                await Staff.updateMany({ hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:{$nin:staff.receptionits},title:"RECEPTIONIST"
                },{$pull:{doctors:staff._id}});
                await Staff.updateMany({ hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:{$in:staff.receptionits},title:"RECEPTIONIST"
            },{$addToSet:{doctors:staff._id}});
            }

            if(profile.title =="RECEPTIONIST"){
                await Staff.updateMany({ hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:{$nin:staff.doctors},title:"DOCTOR"
                },{$pull:{receptionits:staff._id}});
                await Staff.updateMany({ hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:{$in:staff.doctors},title:"DOCTOR"
            },{$addToSet:{receptionits:staff._id}});
            }

        }else if(isPatient){
            let patient = await Patient.findOneAndUpdate({profile:mongoose.Types.ObjectId(profile._id)}, profileObj, { new: true });

            if(isNew) {
                patient.profile = profile._id;
                profile.patient = patient._id;
                await staff.save();
                await profile.save();
            }

        }else if(isSupplier){
            let supplier = await Supplier.findOneAndUpdate({profile:mongoose.Types.ObjectId(profile._id)}, profileObj, { new: true });

            if(isNew) {
                supplier.profile = profile._id;
                profile.supplier = supplier._id;
                await supplier.save();
                await profile.save();
            }
        }

        if (!profile) throw new APIError(400, 'cannot update profile');

        
        profile = await profile.populate([{path: "staff"} , {path: 'patient'} ,{path: "supplier"}]).execPopulate();
        profile._doc = restructureProfileObj(profile,true);
        socket.socket.emitToRoom(user.profile.hospital._id,profile._id+"",{profile:user.profile,profile,operation:'update',socketID});
        socket.socket.emitToRoom(user.profile.hospital._id,user.profile.hospital._id+"-refresh-storage",{socketID});

        return profile;
    }
    async findOneProfile(profileID, assignedID, email, user) {
        let query = { hospital: mongoose.Types.ObjectId(user.profile.hospital._id) };
        if (!profileID && !assignedID && !email) throw new APIError(404, 'missinbg ids');
        if (profileID) query._id = mongoose.Types.ObjectId(profileID);
        if (email) query.email = email;
        if (assignedID) query.assignedID = assignedID;

        let profile = await this.model.findOne(query);
        return profile;
    }
    async findProfiles(filters, page, limit, user) {

        let admins = await Staff.find({isAdmin: true});
        admins = admins.map(a => a.profile);
        let query = { 
            hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
            $or:[{title:"DOCTOR",staff:{$in:user.profile.staff.doctors}},{_id:{$in : admins}},{title:{$ne:"DOCTOR"}}]
        };
        //profileIDs
        if (filters.profileIDs) {
            if (!query["$and"]) query["$and"] = [];
            query["$and"].push({ $or: [{ _id: { $in: filters.profileIDs } }, { staff: { $in: filters.profileIDs } }, { patient: { $in: filters.profileIDs } }, { supplier: { $in: filters.profileIDs } }, { superAdmin: { $in: filters.profileIDs } }] });
        };
        //on name
        if (filters.name) {
            if (!query["$and"]) query["$and"] = [];
            query["$and"].push({ $or: [{ firstName: { $regex: filters.name, $options: "i" } }, { lastName: { $regex: filters.name, $options: "i" } }] });
        };

        //on gender
        if (filters.gender) query.gender = filters.gender;

        //profileNumber
        if (filters.profileNumber) query.profileNumber = { $regex: filters.profileNumber, $options: "i" };

        //email
        if (filters.email) query.email = { $regex: filters.email, $options: "i" };

        //isAdmin
        if (filters.isAdmin){
            if (!query["$and"]) query["$and"] = [];
            query["$and"].push({_id : {$in : [admins]}});
        }

        //titles
        if (filters.titles) {
            if (!query["$and"]) query["$and"] = [];
            filters.titles = filters.titles.map(x => Object.assign({}, {
                title: x
            }));
            query["$and"].push({ $or: filters.titles });
        }

        //positions
        if (filters.positions) {
            let profiles = await Staff.find({position : {$in : filters.positions}})
            profiles = profile.map(p => p.profile);
            if (!query["$and"]) query["$and"] = [];
            query["$and"].push({_id : {$in : [profiles]}});
        }

        //residencies
        if (filters.residencies) {
            let profiles = await Staff.find({residencies : {$in : filters.residenciess}})
            profiles = profile.map(p => p.profile);
            if (!query["$and"]) query["$and"] = [];
            query["$and"].push({_id : {$in : [profiles]}});
        }

        //seniorities
        if (filters.seniorities) {
            let profiles = await Staff.find({seniorities : {$in : filters.senioritiess}})
            profiles = profile.map(p => p.profile);
            if (!query["$and"]) query["$and"] = [];
            query["$and"].push({_id : {$in : [profiles]}});
        };
        //searchText
        if (filters.searchText) {
            if (!query["$and"]) query["$and"] = [];
            let or = [{ firstName: { $regex: filters.searchText, $options: "i" } }, { lastName: { $regex: filters.searchText, $options: "i" } }, { email: { $regex: filters.searchText, $options: "i" } }, { assignedID: { $regex: filters.searchText, $options: "i" } }, { phoneNumber: { $regex: filters.searchText, $options: "i" } }];
            if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
            query["$and"].push({ $or: or });
        }

        let options = {
            sort: { title: 1, seniority: -1, firstName: 1, lastName: 1, startingDate: 1 },
            page: parseInt(page, 10) || 1,
            limit: parseInt(limit, 10) || 1000
        };

        let profiles = await this.model.paginate(query, options);
        if (!profiles) throw new APIError(404, 'cannot find profiles');
        profiles.docs = profiles.docs.map(p => {
            p._doc = restructureProfileObj(p,true)
            return p
        })

        return profiles;
    }

    async getProfilePic(profileID) {
        let profilePic = await this.model.findById(profileID, 'profilePic -_id');
        if (!profilePic) throw new APIError(404, 'cannot find profilePic');
        return profilePic;
    }
    async getPatients (filters, page, limit, user){
        filters.titles=['PATIENT'];
        let patients=await this.findProfiles(filters,page,limit,user);
        if (!patients) throw new APIError(404, 'cant find patients');

        let patientIDs=patients.docs.map(x=>x._id);
        /*let sessions=await Session.find({
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
                patient:{$in:patientIDs}
            });
            patients.docs=patients.docs.map(p=>{
                let sessionCounts=sessions.filter(s=>s.patient._id+""==p._id+"").length;
                p._doc.sessionCounts=sessionCounts;
                return p;
            })*/
            return patients

    }
    async patientEditPatient(token,profile,files,socketID){
        let patient = jwt.verify(token, process.env.JWT_SECRET_KEY || JWT_SECRET_KEY, {
            algorithms: ['HS256']
          });
          if(!patient.patient || !patient.hospital || !patient.appointment)
          throw new APIError(403, 'token not valid');
          let query={_id:mongoose.Types.ObjectId(patient.patient),hospital:mongoose.Types.ObjectId(patient.hospital)};
           profile = await this.model.findOneAndUpdate(query,profile,{new:true});
           if(!profile) throw new APIError(403, 'cannot update profile');
           let appointment;
           if(files){
                 appointment=await Appointment.findOneAndUpdate({_id:mongoose.Types.ObjectId(patient.appointment),hospital:mongoose.Types.ObjectId(patient.hospital)},
               {files:files},
               {new:true}
               );
           }
           socket.socket.emitToRoom(patient.hospital,patient.patient+"",{profile:profile,operation:'update',socketID});
           if(appointment)
           socket.socket.emitToRoom(patient.hospital,patient.appointment+"",{appointment,operation:'update',socketID});

          return {profile,appointment};
    }

    async patientGetPatient(token){
        let patient = jwt.verify(token, process.env.JWT_SECRET_KEY || JWT_SECRET_KEY, {
            algorithms: ['HS256']
          });
          if(!patient.patient || !patient.hospital || !patient.appointment)
          throw new APIError(403, 'token not valid');
          let query={_id:mongoose.Types.ObjectId(patient.patient),hospital:mongoose.Types.ObjectId(patient.hospital)};
          let profile = await this.model.findOne(query).populate('hospital','name address localPhone phoneNumber');
          if(!profile)
          throw new APIError(403, 'token not valid');
          let appointment;
          if(patient.appointment){
               appointment=await Appointment.findOne({_id:mongoose.Types.ObjectId(patient.appointment),hospital:mongoose.Types.ObjectId(patient.hospital)});
               if(!appointment)
               throw new APIError(403, 'token not valid');
            }
          
          return {profile,appointment};
    }

    async editLang(lang,user){
        let query={
            _id:mongoose.Types.ObjectId(user.profile._id),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
        }
        let profile=await this.model.findOneAndUpdate(query,{language:lang},{new:true}).select('language');
        if(!profile)throw new APIError(403, 'profile not found');
        return profile;
    }
    
}

export default ProfileService;