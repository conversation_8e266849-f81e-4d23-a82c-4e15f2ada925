@import '../../../../theming/variables';
@import '../../../../theming/shared.styles';

.create-profile-container {
  position: relative;

  ::ng-deep .ngx-timepicker-control__arrows {
    top: -5px !important
  }

  ::ng-deep .ngx-timepicker {

    height: 39px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.30) !important;
  }

  ::ng-deep .timepicker-backdrop-overlay {
    z-index: 1000 !important;
  }

  ::ng-deep .timepicker-overlay {
    z-index: 1000 !important;
  }

  .mat-list-icon {
    color: rgba(0, 0, 0, 0.54);
  }

  .file-container {
    border: 1px solid $color-primary;
    border-radius: 5px;
    padding: 5px;
    margin-bottom: 5px;
  }

  .download-button {
    color: $color-primary;
    margin-right: 7px;
    cursor: pointer;
  }

  .delete-button {
    color: darken($color-warn, 10);
    cursor: pointer;
  }

  app-avatar {
    cursor: pointer;

  }

  #picture-input {
    visibility: hidden;
  }

  mat-divider {
    margin-left: 10px;
    margin-right: 10px;
  }

  .basic-info-container[dir="ltr"] {
    width: 100%;

    div {
      mat-form-field:first-child {
        margin-right: 10px;
      }
    }
  }

  .basic-info-container[dir="rtl"] {
    width: 100%;

    div {
      mat-form-field:first-child {
        margin-left: 10px;
      }
    }
  }

  .extra-info-container {
    width: 500px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;

    img {
      width: 20px;
      height: 20px;
      margin-top: -12px;
    }

    ::ng-deep .mat-chip-list-wrapper {
      margin-top: 20px;
    }

  }

  .seperator-content {
    margin-top: 10px;
  }
}

app-circle-button {
  margin-bottom: 3px;
}

.profiles-label {
  font-size: 12px;
}
